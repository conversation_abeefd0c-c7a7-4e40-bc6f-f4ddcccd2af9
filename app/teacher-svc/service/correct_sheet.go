package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"os"
	"sort"
	"strings"
	"sync"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	appModel "codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/model"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/utils"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonutils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
	"github.com/spf13/viper"
)

const (
	CorrectSheetTimeout = 8 * time.Minute
)

// var exampleCorrectResult = map[string]any{
// "student_number": "string, 试卷上的学号信息，例如 19331288",
// "answer": []map[string]any{
// {
// "number": "string, 题号，如果题目有小题则格式为：大题(小题)，例如1(2)表示第1大题的第2小题",
// "score":  "float, 这道题的得分，参考答案没有给出分数则填-1",
// "answer": "string, 学生在答题卡上的答案完整内容",
// "reason": "string, 按点给分的题目中，需要给出给分理由及标准答案(不含解析)",
// "right":  "float, 必填，0-1 的数，代表当前学生作答与参考答案的匹配程度",
// "top_left": map[string]any{
// "x": "int, 作答区域左上角x坐标",
// "y": "int, 作答区域左上角y坐标",
// },
// "bottom_right": map[string]any{
// "x": "int, 作答区域右下角x坐标",
// "y": "int, 作答区域右下角y坐标",
// },
// },
// },
// }
// var parseCorrectResultSchema = commonutils.GenerateSchema[model.LLMCorrectResult]()

func CorrectSheet(ctx context.Context, info *model.CostControlInfo, sheetURLs []string, answerURLs []string) (any, *model.CorrectResult, error) {
	logs := logger.GetLogger(ctx)
	// byteParseCorrectResultSchema, _ := parseCorrectResultSchema.MarshalJSON()
	// strParseCorrectResultSchema := string(byteParseCorrectResultSchema)
	client := GetOrInitVolcengineClient()
	// client := GetOrInitBailianClient()

	// 识别选择题区域
	multipleChoiceAnswers, err := recSheetSelectionAreaAndRecResult(ctx, info, sheetURLs)
	if err != nil {
		logs.WithError(err).Errorf("fail to recognize multiple choice answers")
		// 失败不阻塞后续的判分和识别
	}

	// 构建系统提示
	systemPrompt := `# 角色
你是一名专业且严谨的阅卷老师，精通各个学科知识，你的工作是对照答案，对学生的答题卡进行逐题细致批改和准确判分。

## 技能
### 技能 1: 全科批改
1. 仔细分析答题卡图片内容，识别出学生的作答内容。识别要求如下：
	1.1 选择题部分，如果有给出识别结果，则直接根据给出的识别结果去比对，不需要再去识别给出的图片中的作答结果；如果没有给出识别结果，则需要识别出学生的填涂情况，得到学生作答后进行打分。
	1.2 识别每道题的分数(非批改判分流程，而是识别单题分数的流程)时，优先使用参考答案的分数标准；若参考答案未给出得分，则从答题卡中获取得分；若答题卡中也无法识别得分，则在判分时输出"-1"。
	1.3 对于大题，你需要基于参考答案中的关键步骤，识别出大题学生作答的关键步骤，并在判分依据中按步骤说明给分依据
2. 按照答题卡上题目的顺序，逐题对比参考答案与学生在答题卡上的答案，严格依据参考答案的内容对答题卡上的答题结果进行判分。
3. 对试卷批改内容输出内容包括:
   3.1 **题号**: 大题题号和小题题号（例如"12(1)"）。“一、选择题”这类标题不算大题题号。
   3.2 **匹配程度**: 一个0-1之间的浮点数。客观题正确为"1.0"，错误为"0"。主观题的匹配程度 = 学生答对的得分点数量 / 总得分点数量。
   3.3 **学生得分**: 一个浮点数，代表当前题目学生作答的得分，得分是基于参考答案中的判分规则，以及该题的总分进行计算。
   3.4 **判分依据**: 你对这道题给出的得分，依据是什么
   	3.4.1 如果参考答案有给出判分规则，则必须根据判分规则，逐点给出判分依据。
   	3.4.2 对选择、填空等客观题或无详细规则的题目，则只需要在学生作答但作答错误时给出说明即可，如果学生无作答或作答是正确的，则不需要给出说明。
	3.4.3 对大题和主观题这种按点给分的题目，需要基于学生作答和参考答案，给出给分的理由。即使学生的作答是正确的，也要给出正确的点。
	3.4.4 所有扣分的地方，都需要说明学生作答是什么，哪里错了导致了扣分
	3.4.5 不要输出数学公式，而是用文字描述
4. 如果答题卡上明确有写出学号或姓名信息的话，那么需要在输出中带上学号或姓名信息，如果两个都有，则两者都需要输出。

## 限制:
- 仅围绕全科题目批改相关内容进行回复。
- 学生的每一道作答，都需要进行批改，不可遗漏。
- 如果答题卡上明确包含多个不同学生的学号，则停止任务并输出：“这张答题卡包含了多位学生的作答，无法进行批改”。
- 如果学生的某处作答因字迹潦草或表达模糊而无法明确判断，请在该题的“判分依据”中注明“作答无法识别”，并将该题得分记为"0"，匹配度记为"0"。
- 你只需要对照参考答案进行批改，不需关心参考答案本身的正确与否。`

	// 构建消息
	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemPrompt),
	}

	// 添加参考答案
	answerPrompt := "# 输入\n## 参考答案\n"
	messages = append(messages, openai.UserMessage(answerPrompt))
	for _, answerURL := range answerURLs {
		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    answerURL,
					Detail: "high",
				},
			}},
		}))
	}

	// 添加学生答题卡图片
	messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
		{OfText: &openai.ChatCompletionContentPartTextParam{
			Text: "## 学生答题卡图片\n",
		}},
	}))
	for _, sheetURL := range sheetURLs {
		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    sheetURL,
					Detail: "high",
				},
			}},
		}))
	}
	if multipleChoiceAnswers != "" {
		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfText: &openai.ChatCompletionContentPartTextParam{
				Text: "## 选择题识别结果\n" + multipleChoiceAnswers,
			}},
		}))
	}

	// 添加输出提示
	messages = append(messages, openai.UserMessage(`# 输出
*注意：括号内的是解释说明的内容，实际输出时参考要求和样例输出*
## 参考输出1：一个完整的输出
==========
学号：19331288
姓名：张三
题号：1
得分：5
匹配程度：1.0
判分依据：(这里不输出是因为这是一道选择题，不需要依据)

题号：2(1)
得分：3
匹配程度：0.5
判分依据：(实际输出时需要基于参考答案，按点给出判分的依据)
==========

## 参考输出2：一张答题卡包含了多位学生的作答
==========
这张答题卡包含了多位学生的作答，无法进行批改
==========

## 批改结果
你的批改结果是：
`))

	isLocal := commonutils.IsLocal()
	var (
		vlmRawOutput     any
		vlmOutputContent string
	)
	// 调用API
	modelName := viper.GetString("volcengine.ark.model.visual")
	// modelName := viper.GetString("aliyun.bailian.model.visual-72b")
	if !isLocal {
		chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
			Messages: messages,
			Model:    modelName,
		}, option.WithJSONSet("thinking", map[string]any{
			"type": "disabled", // 不使用深度思考能力
		}), option.WithRequestTimeout(CorrectSheetTimeout))
		if err != nil {
			logs.WithError(err).Errorf("fail to create chat completion")
			return nil, nil, bizerr.WrapErr(constant.ErrCoze, err)
		}
		logs.Infof("result: %+v", utils.ToJson(chatCompletion))
		vlmRawOutput = chatCompletion
		vlmOutputContent = chatCompletion.Choices[0].Message.Content

		// 记录大模型消耗
		if chatCompletion.Usage.TotalTokens > 0 {
			usageData := appModel.LLMUsageData{
				Model:        modelName,
				InputTokens:  chatCompletion.Usage.PromptTokens,
				OutputTokens: chatCompletion.Usage.CompletionTokens,
				TotalTokens:  chatCompletion.Usage.TotalTokens,
				Context:      "Image correction",
				RawResponse:  utils.ToJson(chatCompletion),
			}

			extra := map[string]string{}
			// 调用记录函数
			if err := RecordCostControlInfo(ctx, info, usageData, extra); err != nil {
				logs.WithError(err).Warn("Failed to record LLM usage data")
			}
		}
	} else {
		resp := client.Chat.Completions.NewStreaming(ctx, openai.ChatCompletionNewParams{
			Messages: messages,
			Model:    modelName,
		}, option.WithJSONSet("thinking", map[string]any{
			"type": "disabled", // 不使用深度思考能力
		}))
		for resp.Next() {
			data := resp.Current()
			content := data.Choices[0].Delta.Content
			fmt.Print(content)
			vlmOutputContent += content
		}
		vlmRawOutput = vlmOutputContent
	}

	// 文本表述转结构体
	parseSystemPromt := `# 角色
你是一名专业的结构化数据解析助手，负责将输入的文本转换为特定的结构化数据格式。
## 技能
### 技能1：将批改结果转成结构化数据
1. 当接收到一段文本时，仔细分析文本内容，将其转换为特定的结构化数据格式。
2. 输出的内容必须严格按照要求的格式进行，不得随意变动。
3. 输出的内容必须是合法的json格式，不需要包含任何其他内容，不要包含任何解释说明，不需要有markdown的代码标识等信息，保证输出是可以直接被json.loads()处理的。
4. 字段说明如下：
4.1 student_number: string, 学生的学号信息
4.2 student_name: string, 学生的姓名信息
4.3 multi_student_number: bool, 是否有多个学号，如果有多个学号则为true，否则为false
4.4 answer: array, 作答结果数组
4.4.1 answer[i].main_question: string, 大题题号，例如1
4.4.2 answer[i].sub_question: string, 小题题号，例如(2)，如果没有小题则为空字符串
4.4.3 answer[i].score: float, 这道题学生的最终得分，未说明则固定填-1
4.4.4 answer[i].reason: string, 给分理由，可能为空
4.4.5 answer[i].right: float, 必填，0-1 的数，代表当前学生作答与参考答案的匹配程度

## 限制:
- 只专注于将输入的文本转换为特定的结构化数据格式，拒绝回答其他无关话题。
- 所输出的内容必须按照要求整理成特定格式的数组项，不能偏离要求。`
	parseMessages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(parseSystemPromt),
	}
	parseMessages = append(parseMessages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
		{OfText: &openai.ChatCompletionContentPartTextParam{
			Text: "## 输入\n" + vlmOutputContent,
		}},
	}))
	parseMessages = append(parseMessages, openai.UserMessage("# 输出\n\n你的输出是："))

	// 调用API
	modelName = viper.GetString("volcengine.ark.model.visual-lite")
	// respFormat := openai.ChatCompletionNewParamsResponseFormatUnion{
	// 	OfJSONSchema: &shared.ResponseFormatJSONSchemaParam{
	// 		Type: "json_schema",
	// 		JSONSchema: shared.ResponseFormatJSONSchemaJSONSchemaParam{
	// 			Name:        "correct-result",
	// 			Description: openai.String("基于输入解析出的学生作答结果"),
	// 			Schema:      parseCorrectResultSchema,
	// 			// Strict:      openai.Bool(true),
	// 		},
	// 	},
	// }
	var parseOutput string
	if !isLocal {
		pareChatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
			Messages: parseMessages,
			Model:    modelName,
			// ResponseFormat: respFormat,
		}, option.WithJSONSet("thinking", map[string]any{
			"type": "disabled",
		}), option.WithRequestTimeout(CorrectSheetTimeout))
		if err != nil {
			logs.WithError(err).Errorf("fail to create chat completion")
			return nil, nil, bizerr.WrapErr(constant.ErrCoze, err)
		}
		parseOutput = pareChatCompletion.Choices[0].Message.Content
		logs.Infof("parse result: %+v", utils.ToJson(pareChatCompletion))
		if pareChatCompletion.Usage.TotalTokens > 0 {
			usageData := appModel.LLMUsageData{
				Model:        modelName,
				InputTokens:  pareChatCompletion.Usage.PromptTokens,
				OutputTokens: pareChatCompletion.Usage.CompletionTokens,
				TotalTokens:  pareChatCompletion.Usage.TotalTokens,
				Context:      "Image correction parse",
				RawResponse:  utils.ToJson(pareChatCompletion),
			}
			if err := RecordCostControlInfo(ctx, info, usageData, map[string]string{}); err != nil {
				logs.WithError(err).Warn("Failed to record LLM usage data")
			}
		}
	} else {
		resp := client.Chat.Completions.NewStreaming(ctx, openai.ChatCompletionNewParams{
			Messages: parseMessages,
			Model:    modelName,
			// ResponseFormat: respFormat,
		}, option.WithJSONSet("thinking", map[string]any{
			"type": "disabled",
		}))
		for resp.Next() {
			data := resp.Current()
			content := data.Choices[0].Delta.Content
			fmt.Print(content)
			parseOutput += content
		}
		if resp.Err() != nil {
			logs.WithError(resp.Err()).Errorf("fail to create chat completion")
			return nil, nil, bizerr.WrapErr(constant.ErrCoze, resp.Err())
		}
	}

	parseOutput = strings.TrimSuffix(strings.TrimPrefix(parseOutput, "```json"), "```")
	var result model.CorrectResult
	if err := utils.JSON.UnmarshalFromString(parseOutput, &result); err != nil {
		logs.WithError(err).Errorf("fail to unmarshal question numbers, resp: %s", parseOutput)
		return vlmRawOutput, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}

	return vlmRawOutput, &result, nil
}

// recSheetSelectionAreaAndRecResult识别出给出图片的选择题作答区域，并识别出作答结果
func recSheetSelectionAreaAndRecResult(ctx context.Context, info *model.CostControlInfo, imageURLs []string) (string, error) {
	logs := logger.GetLogger(ctx)
	client := GetOrInitVolcengineClient()
	// imgClient := GetImageService()

	// 第一步：并发处理每张图片识别选择题作答区域
	type imageResult struct {
		idx          int
		croppedPaths []string
		vlmOutput    any
		err          error
	}

	resultChan := make(chan imageResult, len(imageURLs))
	var wg sync.WaitGroup

	for i, imageURL := range imageURLs {
		idx := i
		wg.Add(1)
		go func(url string) {
			defer wg.Done()

			result := imageResult{idx: idx}

			// 构建识别选择题区域的prompt
			prompt := "框出图片中填涂答题卡的选择题作答区域的位置，输出 bounding box 的坐标。请识别所有选择题作答区域，并保证只输出选择题作答区域，并对每个区域输出一组坐标。如果没有选择题作答区域，则输出：无选择题作答区域"

			messages := []openai.ChatCompletionMessageParamUnion{
				openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
					{OfImageURL: &openai.ChatCompletionContentPartImageParam{
						ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
							URL:    url,
							Detail: "high",
						},
					}},
					{OfText: &openai.ChatCompletionContentPartTextParam{
						Text: prompt,
					}},
				}),
			}

			// 调用大模型识别区域
			modelName := viper.GetString("volcengine.ark.model.visual-lite")
			var bboxContent string

			chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
				Messages: messages,
				Model:    modelName,
			}, option.WithJSONSet("thinking", map[string]any{
				"type": "disabled",
			}), option.WithRequestTimeout(CorrectSheetTimeout))
			if err != nil {
				logs.WithError(err).Errorf("fail to create chat completion for bbox detection")
				result.err = err
				resultChan <- result
				return
			}
			bboxContent = chatCompletion.Choices[0].Message.Content
			result.vlmOutput = chatCompletion

			// 第二步：解析坐标并切图
			coordinates, err := parseBoundingBoxCoordinates(ctx, bboxContent)
			if err != nil {
				logs.WithError(err).Errorf("fail to parse bounding box coordinates")
				result.err = err
				resultChan <- result
				return
			}

			// 对每组坐标调用DrawBoundingBox切图
			for _, coord := range coordinates {
				croppedImagePath, err := drawBoundingBox(ctx, url, coord)
				if err != nil {
					logs.WithError(err).Errorf("fail to crop image with bounding box")
					continue
				}

				result.croppedPaths = append(result.croppedPaths, croppedImagePath)
				// 第三步：对切出来的图片调用HighlightFilledOptions增强
				// enhancedImagePath, err := imgClient.HighlightOptionsViaAPI(croppedImagePath, 0.3)
				// if err != nil {
				// 	logs.WithError(err).Errorf("fail to highlight filled options")
				// 	// 如果增强失败，使用原始切图
				// 	enhancedImagePath = croppedImagePath
				// }

				// result.croppedPaths = append(result.croppedPaths, enhancedImagePath)
			}

			resultChan <- result
		}(imageURL)
	}

	// 等待所有goroutine完成
	wg.Wait()
	close(resultChan)

	// 收集结果
	var allCroppedImagePaths []string

	allResults := make([]imageResult, 0, len(imageURLs))
	for result := range resultChan {
		if result.err != nil {
			logs.WithError(result.err).Error("error processing image")
			continue
		}
		allResults = append(allResults, result)
	}
	sort.Slice(allResults, func(i, j int) bool {
		return allResults[i].idx < allResults[j].idx
	})
	for _, result := range allResults {
		allCroppedImagePaths = append(allCroppedImagePaths, result.croppedPaths...)
	}

	// 第四步：将所有处理后的图片一次性调用大模型识别选择题结果
	if len(allCroppedImagePaths) == 0 {
		logs.Warn("no cropped images found")
		return "", nil
	}

	recognitionResult, err := recognizeMultipleChoiceAnswers(ctx, info, allCroppedImagePaths)
	if err != nil {
		logs.WithError(err).Errorf("fail to recognize multiple choice answers")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

	return recognitionResult, nil
}

// BoundingBoxCoordinate 表示边界框坐标
type BoundingBoxCoordinate struct {
	X1 int `json:"x1"`
	Y1 int `json:"y1"`
	X2 int `json:"x2"`
	Y2 int `json:"y2"`
}

// parseBoundingBoxCoordinates 使用LLM解析大模型返回的边界框坐标
func parseBoundingBoxCoordinates(ctx context.Context, content string) ([]BoundingBoxCoordinate, error) {
	logs := logger.GetLogger(ctx)
	client := GetOrInitVolcengineClient()

	// 构建解析坐标的系统提示
	systemPrompt := `# 角色
你是一个专业的坐标解析助手，专注于从文本中提取边界框坐标信息。

## 技能
### 技能 1: 坐标提取
1. 从输入的文本中识别出所有的边界框坐标
2. 坐标通常以 x1, y1, x2, y2 的形式出现，表示左上角和右下角的坐标
3. 坐标可能以不同的格式出现，如：
   - "x1=100, y1=200, x2=300, y2=400"
   - "(100, 200, 300, 400)"
   - "左上角(100,200)，右下角(300,400)"
   - 或其他自然语言描述

## 输出格式
请严格按照以下JSON格式输出所有识别到的坐标：
[
  {"x1": 100, "y1": 200, "x2": 300, "y2": 400},
  {"x1": 500, "y1": 600, "x2": 700, "y2": 800}
]

## 限制
- 如果没有找到任何坐标，输出空数组 []
- 坐标值必须是整数
- 确保 x2 > x1 且 y2 > y1`

	// 构建消息
	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemPrompt),
		openai.UserMessage(fmt.Sprintf("请从以下文本中提取边界框坐标：\n\n%s", content)),
	}

	// 调用API
	modelName := viper.GetString("volcengine.ark.model.visual-lite")
	var parseContent string

	chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    modelName,
	}, option.WithJSONSet("thinking", map[string]any{
		"type": "disabled",
	}), option.WithRequestTimeout(CorrectSheetTimeout))
	if err != nil {
		logs.WithError(err).Errorf("fail to create chat completion for coordinate parsing")
		return []BoundingBoxCoordinate{}, nil // 返回空数组而不是错误
	}
	parseContent = chatCompletion.Choices[0].Message.Content

	// 解析JSON结果
	var coordinates []BoundingBoxCoordinate
	if err := utils.JSON.UnmarshalFromString(parseContent, &coordinates); err != nil {
		logs.WithError(err).Warnf("failed to parse coordinates from LLM response: %s", parseContent)
		return []BoundingBoxCoordinate{}, nil // 返回空数组而不是错误
	}

	logs.Infof("parsed %d coordinates from content", len(coordinates))
	return coordinates, nil
}

// drawBoundingBox 根据坐标切出图片区域
func drawBoundingBox(ctx context.Context, imageURL string, coord BoundingBoxCoordinate) (string, error) {
	logs := logger.GetLogger(ctx)
	imgClient := GetImageService()

	// 下载图片到临时文件
	resp, err := http.Get(imageURL)
	if err != nil {
		logs.WithError(err).Errorf("fail to download image from url: %s", imageURL)
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logs.Errorf("fail to download image, status code: %d, url: %s", resp.StatusCode, imageURL)
		return "", bizerr.NewBizErrf(constant.ErrServer, "download image failed with status: "+resp.Status)
	}

	// 创建临时文件保存原图
	tempFile, err := os.CreateTemp("/tmp", "original-image-*.jpg")
	if err != nil {
		logs.WithError(err).Error("fail to create temp file")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}
	defer tempFile.Close()

	// 将图片内容写入临时文件
	_, err = io.Copy(tempFile, resp.Body)
	if err != nil {
		logs.WithError(err).Error("fail to copy image to temp file")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

	// 使用utils.CropRegionByCoords切图
	// 注意：坐标需要转换为0-1000的比例坐标
	croppedPath, err := imgClient.CropImageViaAPI(
		tempFile.Name(),
		float64(coord.X1), float64(coord.Y1),
		float64(coord.X2), float64(coord.Y2),
	)
	if err != nil {
		logs.WithError(err).Errorf("fail to crop region")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

	return croppedPath, nil
}

// recognizeMultipleChoiceAnswers 识别多个选择题图片的答案
func recognizeMultipleChoiceAnswers(ctx context.Context, info *model.CostControlInfo, imagePaths []string) (string, error) {
	logs := logger.GetLogger(ctx)
	client := GetOrInitVolcengineClient()

	// 构建系统提示
	systemPrompt := `# 角色
你是一个专业的答题卡识别助手，专注于识别选择题的作答结果。

## 技能
### 技能 1: 选择题识别
1. 仔细分析每张选择题图片，识别出学生的填涂情况
2. 对于选择题，黑色或深色遮挡的部分就是填涂的部分，代表了学生的作答结果
3. 识别每道题的题号和对应的选项（A、B、C、D等）
4. 支持单选题和多选题的识别

## 输出格式
请按照以下格式输出识别结果，每道题一行：
题号: 选项
例如：
1: A
2: B,C
3: D

## 限制
- 仅识别选择题的填涂结果
- 如果某题没有填涂或无法识别，输出"未填涂"
- 多选题用逗号分隔选项`

	// 构建消息
	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemPrompt),
	}

	// 添加所有图片
	for i, imagePath := range imagePaths {
		// 将本地图片文件转换为base64
		imageData, err := os.ReadFile(imagePath)
		if err != nil {
			logs.WithError(err).Errorf("fail to read image file: %s", imagePath)
			continue
		}

		base64Image := fmt.Sprintf("data:image/jpeg;base64,%s",
			base64.StdEncoding.EncodeToString(imageData))

		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    base64Image,
					Detail: "high",
				},
			}},
			{OfText: &openai.ChatCompletionContentPartTextParam{
				Text: fmt.Sprintf("第%d张选择题图片", i+1),
			}},
		}))
	}

	// 添加输出提示
	messages = append(messages, openai.UserMessage("请识别以上所有选择题图片中的作答结果："))

	// 调用API
	modelName := viper.GetString("volcengine.ark.model.visual")
	var recognitionContent string

	chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    modelName,
	}, option.WithJSONSet("thinking", map[string]any{
		"type": "disabled",
	}), option.WithRequestTimeout(CorrectSheetTimeout))
	if err != nil {
		logs.WithError(err).Errorf("fail to create chat completion for recognition")
		return "", bizerr.WrapErr(constant.ErrCoze, err)
	}
	recognitionContent = chatCompletion.Choices[0].Message.Content

	// 记录成本控制信息
	if info != nil {
		inputTokens := int64(0)
		outputTokens := int64(0)
		if chatCompletion.Usage.TotalTokens > 0 {
			inputTokens = chatCompletion.Usage.PromptTokens
			outputTokens = chatCompletion.Usage.CompletionTokens
		}

		usageData := appModel.LLMUsageData{
			Model:        modelName,
			InputTokens:  inputTokens,
			OutputTokens: outputTokens,
			TotalTokens:  inputTokens + outputTokens,
			Context:      "Multiple choice recognition",
			RawResponse:  utils.ToJson(chatCompletion),
		}

		extra := map[string]string{
			"function": "recognizeMultipleChoiceAnswers",
			"images":   fmt.Sprintf("%d", len(imagePaths)),
		}

		// 设置记录类型
		costControlInfo := &model.CostControlInfo{
			RecordType: constant.RecordTypeLLMMultipleChoiceRecognition,
			TaskID:     info.TaskID,
			UserID:     info.UserID,
		}

		err = RecordCostControlInfo(ctx, costControlInfo, usageData, extra)
		if err != nil {
			logs.WithError(err).Warn("fail to record cost control info")
		}
	}

	return recognitionContent, nil
}
