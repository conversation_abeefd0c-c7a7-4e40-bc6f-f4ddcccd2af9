package model

import (
	"context"
	"sort"
	"time"

	"codeup.aliyun.com/level-up/public/common/logger"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

// Sheet 学生试卷信息
type Sheet struct {
	gorm.Model

	// 基础信息
	UUID          uuid.UUID  `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	TaskID        uuid.UUID  `gorm:"index;type:uuid"`             // 关联的批改任务ID
	StudentNumber string     `gorm:"type:varchar(64);index"`      // 学号
	StudentName   string     `gorm:"type:varchar(64);index"`      // 姓名
	Status        string     `gorm:"type:varchar(50);default:''"` // 状态
	Score         float64    `gorm:"default:0"`                   // 得分
	TotalScore    float64    `gorm:"default:0"`                   // 总分
	CorrectedAt   *time.Time // 批改完成时间

	// 批改结果关联
	CorrectionResultID *uuid.UUID `gorm:"type:uuid;index"` // 关联的答题卡批改结果ID

	Data               string `gorm:"type:text"`                   // 大模型原始数据
	SegmentationStatus string `gorm:"type:varchar(20);default:''"` // 切题状态：空字符串-未切题，manual-手动切题，applied-自动应用切题
}

// 切题状态
const (
	SegmentationStatusNone    = ""        // 未切题
	SegmentationStatusManual  = "manual"  // 手动切题
	SegmentationStatusApplied = "applied" // 自动应用切题
)

// SheetItem 试卷与上传队列项关联
type SheetItem struct {
	gorm.Model

	// 基础信息
	UUID        uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	SheetID     uuid.UUID `gorm:"index;type:uuid"`             // 关联的试卷ID
	QueueItemID uuid.UUID `gorm:"index;type:uuid;uniqueIndex"` // 关联的上传队列项ID
}

// TableName 返回表名
func (s *Sheet) TableName() string {
	return "sheets"
}

// TableName 返回表名
func (si *SheetItem) TableName() string {
	return "sheet_items"
}

// CreateSheet 创建试卷
func CreateSheet(tx *gorm.DB, sheet *Sheet) error {
	return tx.Create(sheet).Error
}

// CreateSheetItem 创建试卷与上传队列项关联
func CreateSheetItem(tx *gorm.DB, item *SheetItem) error {
	return tx.Create(item).Error
}

// GetSheetsByTaskID 获取任务的所有试卷
func GetSheetsByTaskID(tx *gorm.DB, taskID string, limit, offset int, needTotal bool) ([]*Sheet, int64, error) {
	var sheets []*Sheet
	var total int64 = 0

	query := tx.Model(&Sheet{}).Where("task_id = ?", taskID)

	if needTotal {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		if total == 0 {
			return nil, 0, nil
		}
	}

	query = query.Order("created_at ASC").Offset(offset)
	if limit > 0 {
		query = query.Limit(limit)
	}
	if err := query.Find(&sheets).Error; err != nil {
		return nil, 0, err
	}
	return sheets, total, nil
}

func BatchUpdateSheetsStatus(tx *gorm.DB, sheets []string, status string) error {
	return BatchUpdateSheets(tx, sheets, map[string]interface{}{
		"status": status,
	})
}

func BatchUpdateSheets(tx *gorm.DB, sheetIDs []string, updateMap map[string]interface{}) error {
	return tx.Model(&Sheet{}).Where("uuid IN ?", sheetIDs).Updates(updateMap).Error
}

func GetSheetByItemID(tx *gorm.DB, itemID string) (*Sheet, error) {
	var sheet Sheet
	if err := tx.Table("sheet_items").
		Select("sheets.*").
		Joins("JOIN sheets ON sheet_items.sheet_id = sheets.uuid").
		Where("sheet_items.queue_item_id = ?", itemID).
		First(&sheet).Error; err != nil {
		return nil, err
	}
	return &sheet, nil
}

// GetSheetItems 获取试卷的所有上传队列项
func GetSheetItems(tx *gorm.DB, sheetID string, limit, offset int, needTotal bool) ([]*SheetItem, int64, error) {
	var items []*SheetItem
	var total int64 = 0

	query := tx.Model(&SheetItem{}).Where("sheet_id = ?", sheetID)

	if needTotal {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		if total == 0 {
			return nil, 0, nil
		}
	}

	// Join with upload_queue_items to get the sequence for sorting
	query = tx.Table("sheet_items").
		Select("sheet_items.*").
		Joins("LEFT JOIN upload_queue_items ON sheet_items.queue_item_id = upload_queue_items.uuid").
		Joins("LEFT JOIN upload_queues ON upload_queue_items.queue_id = upload_queues.uuid").
		Where("sheet_items.sheet_id = ?", sheetID).
		Order("upload_queues.created_at ASC").
		Order("ABS(upload_queue_items.sequence) ASC").
		Order("upload_queue_items.sequence DESC").
		Limit(limit).
		Offset(offset)

	if err := query.Find(&items).Error; err != nil {
		return nil, 0, err
	}
	return items, total, nil
}

// GetSheetByID 根据ID获取试卷
func GetSheetByID(tx *gorm.DB, sheetID string) (*Sheet, error) {
	var sheet Sheet
	if err := tx.Where("uuid = ?", sheetID).First(&sheet).Error; err != nil {
		return nil, err
	}
	return &sheet, nil
}

// GetSheetItemsBySheetID 获取试卷的所有上传队列项ID
func GetSheetItemsBySheetID(tx *gorm.DB, sheetID string) ([]*SheetItem, error) {
	var items []*SheetItem
	if err := tx.Where("sheet_id = ?", sheetID).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

// GetSheetByStudentNumber 根据学号获取试卷
func GetSheetByStudentNumber(tx *gorm.DB, taskID string, executionID string, studentNumber string) (*Sheet, error) {
	var sheet Sheet
	if err := tx.Where("task_id = ? AND execution_id = ? AND student_number = ?",
		taskID, executionID, studentNumber).First(&sheet).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &sheet, nil
}

// UpdateSheet 更新试卷
func UpdateSheet(tx *gorm.DB, sheetID string, updateMap map[string]interface{}) error {
	return tx.Model(&Sheet{}).Where("uuid = ?", sheetID).Updates(updateMap).Error
}

// GetCorrectResult 获取批改结果（从新的表结构中获取）
func (sheet *Sheet) GetCorrectResult(ctx context.Context, tx *gorm.DB) *CorrectResult {
	logs := logger.GetLogger(ctx)

	// 如果没有关联的批改结果ID，返回nil
	if sheet.CorrectionResultID == nil {
		return nil
	}

	// 获取答题卡批改结果
	sheetResult, err := GetSheetCorrectionResult(tx, sheet.CorrectionResultID.String())
	if err != nil {
		logs.WithError(err).Error("failed to get sheet correction result")
		return nil
	}
	if sheetResult == nil {
		return nil
	}

	// 获取单题批改结果
	questionResults, err := GetQuestionCorrectionResultsBySheetResultID(tx, sheetResult.UUID.String())
	if err != nil {
		logs.WithError(err).Error("failed to get question correction results")
		return nil
	}
	sort.Slice(questionResults, func(i, j int) bool {
		return questionResults[i].Sequence < questionResults[j].Sequence
	})

	// 转换为旧的CorrectResult格式以保持兼容性
	correctResult := &CorrectResult{
		StudentNumber:      sheetResult.StudentNumber,
		StudentName:        sheetResult.StudentName,
		MultiStudentNumber: sheetResult.MultiStudentNumber,
		Answer:             make([]*CorrectResultItem, len(questionResults)),
	}

	for i, qr := range questionResults {
		correctResult.Answer[i] = &CorrectResultItem{
			MainQuestion: qr.MainQuestion,
			SubQuestion:  qr.SubQuestion,
			Score:        qr.Score,
			Reason:       qr.Reason,
			Right:        qr.Right,
			TopLeft:      qr.GetTopLeft(),
			BottomRight:  qr.GetBottomRight(),
			ItemID:       qr.ItemID,
		}
	}

	return correctResult
}

// SetCorrectResult 设置批改结果（保存到新的表结构中）
func (sheet *Sheet) SetCorrectResult(ctx context.Context, tx *gorm.DB, data *CorrectResult) error {
	logs := logger.GetLogger(ctx)

	// 如果数据为空，直接返回
	if data == nil {
		return nil
	}

	// 创建或更新答题卡批改结果
	var sheetResult *SheetCorrectionResult
	var err error

	if sheet.CorrectionResultID != nil {
		// 更新现有结果
		sheetResult, err = GetSheetCorrectionResult(tx, sheet.CorrectionResultID.String())
		if err != nil {
			logs.WithError(err).Error("failed to get existing sheet correction result")
			return err
		}
	}

	if sheetResult == nil {
		// 创建新的答题卡批改结果
		sheetResult = &SheetCorrectionResult{
			SheetID:            sheet.UUID,
			StudentNumber:      data.StudentNumber,
			StudentName:        data.StudentName,
			MultiStudentNumber: data.MultiStudentNumber,
			Status:             "completed",
		}
		if err := CreateSheetCorrectionResult(tx, sheetResult); err != nil {
			logs.WithError(err).Error("failed to create sheet correction result")
			return err
		}

		// 更新sheet的关联ID
		sheet.CorrectionResultID = &sheetResult.UUID
	} else {
		// 更新现有的答题卡批改结果
		updateMap := map[string]interface{}{
			"student_number":       data.StudentNumber,
			"student_name":         data.StudentName,
			"multi_student_number": data.MultiStudentNumber,
		}
		if err := UpdateSheetCorrectionResult(tx, sheetResult.UUID.String(), updateMap); err != nil {
			logs.WithError(err).Error("failed to update sheet correction result")
			return err
		}
	}

	// 删除旧的单题批改结果
	if err := DeleteQuestionCorrectionResultsBySheetResultID(tx, sheetResult.UUID.String()); err != nil {
		logs.WithError(err).Error("failed to delete old question correction results")
		return err
	}

	// 创建新的单题批改结果
	questionResults := make([]*QuestionCorrectionResult, len(data.Answer))
	for i, answer := range data.Answer {
		qr := &QuestionCorrectionResult{
			SheetCorrectionResultID: sheetResult.UUID,
			ItemID:                  answer.ItemID,
			Sequence:                i,
			MainQuestion:            answer.MainQuestion,
			SubQuestion:             answer.SubQuestion,
			Score:                   answer.Score,
			Reason:                  answer.Reason,
			Right:                   answer.Right,
		}

		// 设置位置信息
		qr.SetTopLeft(answer.TopLeft)
		qr.SetBottomRight(answer.BottomRight)

		questionResults[i] = qr
	}

	if err := BatchCreateQuestionCorrectionResults(tx, questionResults); err != nil {
		logs.WithError(err).Error("failed to create question correction results")
		return err
	}

	return nil
}
