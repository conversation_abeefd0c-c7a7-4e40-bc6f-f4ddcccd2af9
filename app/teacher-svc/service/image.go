package service

import (
	"bytes"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"sync"

	"github.com/spf13/viper"
)

var (
	imageService         *ImageService
	imageServiceInitOnce sync.Once
)

// ImageService holds the base URL for the API server.
type ImageService struct {
	BaseURL string
	Client  *http.Client
}

// GetImageService creates a new client for interacting with the image processing API. It is a singleton.
func GetImageService() *ImageService {
	imageServiceInitOnce.Do(func() {
		imageService = &ImageService{
			BaseURL: viper.GetString("inner.script_host"),
			Client:  &http.Client{},
		}
	})
	return imageService
}

// CropImageViaAPI sends a request to the /images/crop-coords endpoint.
func (c *ImageService) CropImageViaAPI(imagePath string, x_min, y_min, x_max, y_max float64) (string, error) {
	// Prepare the multipart form
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add the image file
	if err := addFileToMultipart(writer, "file", imagePath); err != nil {
		return "", err
	}

	// Add form fields
	writer.WriteField("x_min", fmt.Sprintf("%f", x_min))
	writer.WriteField("y_min", fmt.Sprintf("%f", y_min))
	writer.WriteField("x_max", fmt.Sprintf("%f", x_max))
	writer.WriteField("y_max", fmt.Sprintf("%f", y_max))
	// Add other optional fields if needed, e.g., quality, output_format
	writer.Close()

	// Create and send the request
	endpoint := c.BaseURL + "/api/v1/images/crop-coords"
	req, err := http.NewRequest("POST", endpoint, body)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := c.Client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("api returned non-200 status: %d %s", resp.StatusCode, resp.Status)
	}

	// Save the returned image
	outputPath := getOutputFilename(imagePath, "_cropped_from_api")
	if err := saveResponseBodyToFile(resp.Body, outputPath); err != nil {
		return "", err
	}

	fmt.Printf("Successfully cropped image via API and saved to: %s\n", outputPath)
	return outputPath, nil
}

// HighlightOptionsViaAPI sends a request to the /images/highlight-filled endpoint.
func (c *ImageService) HighlightOptionsViaAPI(imagePath string, fillThreshold float64) (string, error) {
	// Prepare the multipart form
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add the image file
	if err := addFileToMultipart(writer, "file", imagePath); err != nil {
		return "", err
	}

	// Add form fields
	writer.WriteField("fill_threshold", strconv.FormatFloat(fillThreshold, 'f', -1, 64))
	// Add other optional fields like min_size, aspect_ratio, etc. if needed
	writer.Close()

	// Create and send the request
	endpoint := c.BaseURL + "/api/v1/images/highlight-filled"
	req, err := http.NewRequest("POST", endpoint, body)
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	resp, err := c.Client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("api returned non-200 status: %d %s", resp.StatusCode, resp.Status)
	}

	// Save the returned image to a temporary file
	tempFile, err := os.CreateTemp("/tmp", "highlighted_from_api_*.png")
	if err != nil {
		return "", fmt.Errorf("failed to create temp file: %w", err)
	}
	defer tempFile.Close()

	if err := saveResponseBodyToFile(resp.Body, tempFile.Name()); err != nil {
		// Attempt to close and remove the temp file on error
		os.Remove(tempFile.Name())
		return "", err
	}

	fmt.Printf("Successfully highlighted image via API and saved to temp file: %s\n", tempFile.Name())
	return tempFile.Name(), nil
}

// --- Helper Functions ---

// addFileToMultipart is a helper to add a file to a multipart writer.
func addFileToMultipart(writer *multipart.Writer, fieldName, filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("failed to open file %s: %w", filePath, err)
	}
	defer file.Close()

	part, err := writer.CreateFormFile(fieldName, filepath.Base(filePath))
	if err != nil {
		return fmt.Errorf("failed to create form file: %w", err)
	}
	_, err = io.Copy(part, file)
	if err != nil {
		return fmt.Errorf("failed to copy file content: %w", err)
	}
	return nil
}

// saveResponseBodyToFile saves the content of an io.ReadCloser to a file.
func saveResponseBodyToFile(body io.ReadCloser, outputPath string) error {
	outFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file %s: %w", outputPath, err)
	}
	defer outFile.Close()

	_, err = io.Copy(outFile, body)
	if err != nil {
		return fmt.Errorf("failed to write response to file: %w", err)
	}
	return nil
}

// getOutputFilename generates a new filename with a suffix.
func getOutputFilename(originalPath, suffix string) string {
	ext := filepath.Ext(originalPath)
	base := filepath.Base(originalPath)
	nameOnly := base[0 : len(base)-len(ext)]
	return fmt.Sprintf("/tmp/%s%s%s", nameOnly, suffix, ext)
}
