package main

import (
	"context"
	"fmt"
	"sync"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/controller"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/controller/async"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/middleware"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"

	"codeup.aliyun.com/level-up/public/common/clients/aliyun"
	"codeup.aliyun.com/level-up/public/common/clients/aws"
	"codeup.aliyun.com/level-up/public/common/clients/coze"
	"codeup.aliyun.com/level-up/public/common/clients/gorm"

	"codeup.aliyun.com/level-up/public/common/clients/redis"
	"codeup.aliyun.com/level-up/public/common/goroutine"
	"codeup.aliyun.com/level-up/public/common/logger"
	"codeup.aliyun.com/level-up/public/common/middleware/ginmw"
	"codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

var serviceRunMap = map[string]func(){
	"api":   runApiService,
	"async": runAsyncService,
}

func Init() {
	utils.InitViper()
	{
		logger.Init(&logger.LogrusOption{
			Level: logrus.Level(viper.GetInt("logrus.level")),
		})
	}
	aws.InitS3()
	aliyun.InitSMS()
	redis.Init()
	gorm.InitMultiDB()
	model.Init(gorm.GetMultiDB(""))
	{
		coze.GetOrInit()
		coze.GetOrInitCache()
	}
	{
		service.GetOrInitBailianClient()
		service.GetOrInitVolcengineClient()
	}
	// pay.GetOrInitWechatPay()
}

func main() {
	Init()
	var wg sync.WaitGroup
	for _, name := range viper.GetStringSlice("service.enable") {
		f, ok := serviceRunMap[name]
		if !ok {
			continue
		}
		wg.Add(1)
		goroutine.Go(context.Background(), func() {
			defer wg.Done()
			f()
		})
	}
	wg.Wait()
}

func runApiService() {
	logs := logger.GetLogger(context.Background())
	r := gin.Default()
	r.Use(ginmw.LoggerMW)
	authMW := ginmw.AuthMW(constant.AuthTokenKeyPrefix)

	v1 := r.Group("/api/v1")
	{
		{
			v1.POST("/auth/phone/sendCode", controller.SendSMS)
			v1.POST("/auth/phone/loginByPassword", controller.LoginByPhoneAndPasswordHandler)
			v1.POST("/auth/phone/loginByCode", controller.SMSAuth)
		}

		{
			v1.GET("/auth/current", authMW, controller.GetCurrentUser)
			v1.POST("/auth/logout", authMW, controller.LogoutHandler)
		}

		my := v1.Group("/my", authMW)
		{
			my.GET("/tenants", controller.GetCurrentUserTenants)
			my.POST("/password", controller.UpdatePasswordHandler)
		}

		tenants := v1.Group("/tenants", authMW)
		{
			tenants.GET("", controller.GetTenants)
			tenants.GET("/:tenantId", controller.GetTenantDetail)
			tenants.POST("/join", controller.JoinTenant)
			tenants.GET("/join", controller.GetJoinTenantStatus)
		}
	}

	{
		v1.GET("/oss/token", authMW, controller.GetOssTokenHandler(false))
		v1.GET("/oss", authMW, controller.GetPublicOSSFileHandler)

		v1.POST("/printer/scan", authMW, controller.ScanPrinter) // 绑定任务到二维码
		v1.POST("/printer/bind", authMW, controller.BindTask)    // 绑定任务到二维码
	}

	// 批改任务相关接口
	correction := v1.Group("/corrections", authMW)
	{
		correction.POST("", controller.CreateCorrectionTask)               // 创建批改任务
		correction.GET("", controller.ListCorrectionTasks)                 // 获取批改任务列表
		correction.GET("/:taskID", controller.GetCorrectionTask(false))    // 获取批改任务详情
		correction.PUT("/:taskID/settings", controller.UpdateTaskSettings) // 更新任务设置
		correction.GET("/:taskID/items", controller.ListUploadQueueItems)  // 获取当前任务下的全部已上传任务
		correction.GET("/:taskID/sheets", controller.GetSheets)            // 获取试卷列表
		correction.GET("/:taskID/printers", controller.GetCorrectionTaskPrinterList)
		correction.DELETE("/:taskID/printers/:printerID", controller.RemoveTaskPrinter)
		correction.POST("/:taskID/trigger", controller.TriggerCorrection)                 // 触发批改队列项
		correction.POST("/:taskID/mark", controller.MarkTask)                             // 对批改完成的任务进行原卷留痕
		correction.GET("/:taskID/marked-pdf", controller.GetMarkedPDF)                    // 获取整个任务的留痕PDF
		correction.GET("/:taskID/question-accuracy", controller.GetQuestionAccuracyStats) // 获取题目正确率统计
		correction.GET("/:taskID/question-results", controller.GetQuestionResults)        // 获取题目批改结果列表
	}

	correctItems := v1.Group("/correct-items", authMW)
	{
		correctItems.GET("/:itemID", controller.GetUploadQueueItemDetail) // 获取批改任务详情
		correctItems.POST("/:itemID/mark", controller.MarkItem)           // 为单个队列项生成标记图片
	}

	sheets := v1.Group("/sheets", authMW)
	{
		sheets.GET("/:sheetID", controller.GetSheetDetail)                              // 获取试卷详情
		sheets.GET("/:sheetID/items", controller.GetSheetItems)                         // 获取试卷的上传队列项
		sheets.POST("/:sheetID/segmentation/:itemID", controller.SaveSheetSegmentation) // 保存切题结果
		sheets.POST("/:sheetID/mark", controller.MarkSheet)                             // 对试卷的所有项目进行原卷留痕
		sheets.GET("/:sheetID/marked-pdf", controller.GetSheetMarkedPDF)                // 获取整个任务的留痕PDF
	}

	// 切题相关接口
	segmentation := v1.Group("/segmentation", authMW)
	{
		segmentation.POST("/:taskID/apply", controller.ApplySheetSegmentation) // 应用切题结果
	}

	// 内部接口
	inner := r.Group("/inner/v1")
	{
		inner.POST("/correction/run", controller.MatchAndCorrectSheet) // 运行批改任务
	}

	// 打印机相关接口
	printerAPI := r.Group("/printer/api/v1")
	{
		printerAPI.GET("/qrcode", controller.GenerateQRCode) // 生成二维码
		// 无需认证的接口（扫描仪使用）
		printerAPI.GET("/qrcode/info", controller.GetQRCodeInfo) // 获取二维码信息

		// 需要打印机授权的接口
		printerTaskAuth := printerAPI.Group("", middleware.PrinterAuth())
		{
			printerTaskAuth.GET("/oss/token", controller.GetOssTokenHandler(true))

			printerTaskAuth.GET("/correction", controller.GetCorrectionTask(true)) // 获取批改任务详情

			printerTaskAuth.POST("/papers", controller.UpdatePaper)                // 更新试卷和答案
			printerTaskAuth.POST("/upload-queue", controller.CreateUploadQueue)    // 创建上传队列
			printerTaskAuth.POST("/upload-queue/item", controller.SubmitQueueItem) // 提交单张图片
		}
	}

	if err := r.Run(fmt.Sprintf(":%d", viper.GetInt("service.api.port"))); err != nil {
		logs.WithError(err).Errorf("api service start failed")
	}
}

func runAsyncService() {
	logs := logger.GetLogger(context.Background())
	r := gin.Default()
	r.Use(ginmw.LoggerMW)

	r.Handle("POST", "/invoke", async.RunUploadQueue)

	if err := r.Run(fmt.Sprintf(":%d", viper.GetInt("service.async.port"))); err != nil {
		logs.WithError(err).Errorf("async service start failed")
	}
}
