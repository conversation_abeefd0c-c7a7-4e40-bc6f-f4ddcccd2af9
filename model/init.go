package model

import (
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

func Init(db *gorm.DB) {
	if !viper.GetBool("db.migrate") {
		return
	}
	if err := db.AutoMigrate(
		&User{}, &Tenant{}, &TenantUser{},
		&TenantUserRole{}, &Role{},
		&TenantBalance{},
		&CorrectionTask{},
		&PrinterAuth{},
		&UploadQueue{}, &UploadQueueItem{},
		&Sheet{}, &SheetItem{},
		&CostControlRecord{},
		&SheetCorrectionResult{}, &QuestionCorrectionResult{},
	); err != nil {
		panic(err)
	}
}
