package controller

import (
	"context"
	"net/http"
	"sort"
	"sync"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	monoutils "codeup.aliyun.com/level-up/correct-king/monorepo-backend/utils"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormclient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/goroutine"
	"codeup.aliyun.com/level-up/public/common/logger"
	"codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

const (
	ReferenceSheetPageCount = 2 // 参考用的前面几页数量
)

func MatchAndCorrectSheet(c *gin.Context) {
	logs := logger.GetLogger(c)
	db := gormclient.GetMultiDB("")
	var err error

	var req service.MatchAndCorrectSheetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Errorf("fail to bind request, err: %v", err)
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	var sheets []*model.Sheet
	if req.QueueID != "" {
		sheets, err = service.FindToBeHandledQueueSheets(c, db, req.QueueID)
		if err != nil {
			logs.WithError(err).Errorf("fail to combine item to sheet, err: %v", err)
			c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(err))
			return
		}
	} else if req.SheetID != "" {
		sheet, err := model.GetSheetByID(db, req.SheetID)
		if err != nil {
			logs.WithError(err).Errorf("fail to get sheet, err: %v", err)
			c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
			return
		}
		sheets = []*model.Sheet{sheet}
	}
	if len(sheets) == 0 {
		c.JSON(http.StatusOK, gin.H{})
		return
	}

	// 执行批改动作
	if err := correctSheets(c, db, sheets); err != nil {
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(err))
		return
	}

	c.JSON(http.StatusOK, gin.H{})
}

func correctSheets(ctx context.Context, db *gorm.DB, sheets []*model.Sheet) error {
	logs := logger.GetLogger(ctx)
	// todo: 调整限流
	wg := sync.WaitGroup{}
	for idx := range sheets {
		sheet := sheets[idx]
		wg.Add(1)
		goroutine.Go(ctx, func() {
			defer wg.Done()
			if err := correctSheet(ctx, db, sheet); err != nil {
				logs.WithError(err).Errorf("fail to correct sheet, sheetID: %s", sheet.UUID)
			}
		})
	}
	wg.Wait()
	return nil
}

func correctSheet(ctx context.Context, db *gorm.DB, sheet *model.Sheet) error {
	logs := logger.GetLogger(ctx)
	// 更新批改状态
	if err := model.UpdateSheet(db, sheet.UUID.String(), map[string]interface{}{
		"status": model.CorrectionSheetStatusInProgress,
	}); err != nil {
		logs.WithError(err).Errorf("fail to update sheet, taskID: %s, sheetID: %s", sheet.TaskID, sheet.UUID)
		return bizerr.WrapErr(constant.ErrDB, err)
	}

	var (
		finalCorrectStatus string
		failReason         any
	)
	defer func() {
		if finalCorrectStatus == "" {
			return
		}
		updateMap := map[string]interface{}{
			"status": finalCorrectStatus,
		}
		if failReason != nil {
			updateMap["data"] = finalCorrectStatus
		}
		if err := model.UpdateSheet(db, sheet.UUID.String(), updateMap); err != nil {
			logs.WithError(err).Errorf("fail to update sheet, taskID: %s, sheetID: %s", sheet.TaskID, sheet.UUID)
		}
	}()

	// 获取批改任务，得到答案信息
	task, err := model.GetCorrectionTask(db, sheet.TaskID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get correction task, taskID: %s", sheet.TaskID.String())
		finalCorrectStatus = model.CorrectionSheetStatusFailed
		failReason = err.Error()
		return bizerr.WrapErr(constant.ErrDB, err)
	}
	if task == nil {
		logs.Warnf("task not found, taskID: %s", sheet.TaskID.String())
		finalCorrectStatus = model.CorrectionSheetStatusFailed
		failReason = "task not found"
		return nil
	}
	answers := task.GetAnswerFileIDsArray()
	if len(answers) == 0 {
		logs.Warnf("task has no answer file, taskID: %s", task.UUID.String())
		return nil
	}
	// 获取sheet下的全部原始items
	items, err := model.GetUploadQueueItemsBySheetID(db, sheet.UUID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get upload queue items by sheet id, sheetID: %s", sheet.UUID.String())
		finalCorrectStatus = model.CorrectionSheetStatusFailed
		failReason = err.Error()
		return bizerr.WrapErr(constant.ErrDB, err)
	}
	// 按照seq大小和正负排序
	sort.Slice(items, func(i, j int) bool {
		iAbsSeq := utils.IntAbs(items[i].Sequence)
		jAbsSeq := utils.IntAbs(items[j].Sequence)
		if iAbsSeq != jAbsSeq {
			return iAbsSeq < jAbsSeq
		}
		return items[i].Sequence > items[j].Sequence
	})
	// 生成url图片并提交批改
	var answerFiles, sheetFiles []string
	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	for _, ans := range answers {
		answerFileURL, err := signOSSUrl(ctx, endpoint, bucketName, ans, true, GetPublicOssTTL, nil)
		if err != nil {
			logs.WithError(err).Errorf("fail to sign oss url %s", ans)
			finalCorrectStatus = model.CorrectionSheetStatusFailed
			failReason = err.Error()
			return bizerr.WrapErr(constant.ErrOSS, err)
		}
		answerFiles = append(answerFiles, answerFileURL)
	}
	for _, item := range items {
		sheetFileURL, err := signOSSUrl(ctx, endpoint, bucketName, item.FileID, true, GetPublicOssTTL, nil)
		if err != nil {
			logs.WithError(err).Errorf("fail to sign oss url %s", item.FileID)
			finalCorrectStatus = model.CorrectionSheetStatusFailed
			failReason = err.Error()
			return bizerr.WrapErr(constant.ErrOSS, err)
		}
		sheetFiles = append(sheetFiles, sheetFileURL)
	}
	// 创建CostControlInfo
	costControlInfo := &model.CostControlInfo{
		RecordType: constant.RecordTypeLLMImageCorrection,
		TaskID:     task.UUID.String(),
		UserID:     task.UserID.String(),
	}
	resp, correctData, err := service.CorrectSheet(ctx, costControlInfo, sheetFiles, answerFiles)
	defer func() {
		if resp == nil {
			return
		}
		if err := model.UpdateSheet(db, sheet.UUID.String(), map[string]interface{}{
			"data": monoutils.ToJson(resp),
		}); err != nil {
			logs.WithError(err).Errorf("fail to set upload sheet llm data, sheetID: %s", sheet.UUID)
		}
	}()
	if err != nil {
		logs.WithError(err).Errorf("fail to run correction to correct sheet, taskID: %s, sheetID: %s", task.UUID, sheet.UUID)
		finalCorrectStatus = model.CorrectionSheetStatusFailed
		failReason = err.Error()
		return bizerr.WrapErr(constant.ErrCoze, err)
	}
	// 存入sheet的批改结果
	if err := sheet.SetCorrectResult(ctx, db, correctData); err != nil {
		logs.WithError(err).Errorf("fail to set correct result, sheetID: %s", sheet.UUID)
		finalCorrectStatus = model.CorrectionSheetStatusFailed
		return bizerr.WrapErr(constant.ErrDB, err)
	}
	updateMap := map[string]interface{}{
		"status":               model.CorrectionSheetStatusCompleted,
		"correction_result_id": sheet.CorrectionResultID,
		"corrected_at":         time.Now(),
	}
	if err := model.UpdateSheet(db, sheet.UUID.String(), updateMap); err != nil {
		logs.WithError(err).Errorf("fail to update sheet, taskID: %s, sheetID: %s", task.UUID, sheet.UUID)
		finalCorrectStatus = model.CorrectionSheetStatusFailed
		return bizerr.WrapErr(constant.ErrDB, err)
	}

	// 更新任务的MarkedFileChanged标志
	if err := model.UpdateCorrectionTask(db, task.UUID.String(), map[string]interface{}{
		"marked_file_changed": true,
	}); err != nil {
		logs.WithError(err).Errorf("fail to update task marked file changed flag, taskID: %s", task.UUID)
		// 不中断流程
	}

	return nil
}
