package constant

// LLM cost control record types
const (
	// RecordTypeLLMPaperRecognition represents the cost of recognizing paper content
	RecordTypeLLMPaperRecognition = "llm_paper_recognition"

	// RecordTypeLLMAnswerRecognition represents the cost of recognizing reference answer content
	RecordTypeLLMAnswerRecognition = "llm_answer_recognition"

	// RecordTypeLLMAnswerSheetRecognition represents the cost of recognizing student answer sheet content
	RecordTypeLLMAnswerSheetRecognition = "llm_answer_sheet_recognition"

	// RecordTypeLLMQuestionNumberRecognition represents the cost of recognizing question numbers from images
	RecordTypeLLMQuestionNumberRecognition = "llm_question_number_recognition"

	// RecordTypeLLMTextCorrection represents the cost of correcting student answers in text format
	RecordTypeLLMTextCorrection = "llm_text_correction"

	// RecordTypeLLMImageCorrection represents the cost of correcting student answers from images
	RecordTypeLLMImageCorrection = "llm_image_correction"

	// RecordTypeLLMMultipleChoiceRecognition represents the cost of recognizing multiple choice answers
	RecordTypeLLMMultipleChoiceRecognition = "llm_multiple_choice_recognition"
)
